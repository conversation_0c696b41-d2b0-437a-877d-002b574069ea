<?xml version='1.0' encoding='utf-8'?>
<widget id="com.unvired.permit" version="99.99.99" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <name>Safe Work Permit</name>
    <description>An awesome Ionic/Cordova app.</description>
    <author email="<EMAIL>" href="http://ionicframework.com/">Ionic Framework Team</author>
    <content src="index.html" />
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <preference name="ScrollEnabled" value="false" />
    <preference name="BackupWebStorage" value="none" />
    <preference name="SplashMaintainAspectRatio" value="true" />
    <preference name="FadeSplashScreenDuration" value="300" />
    <preference name="SplashShowOnlyFirstTime" value="false" />
    <preference name="SplashScreen" value="screen" />
    <preference name="SplashScreenDelay" value="3000" />
    <platform name="android">
        <preference name="android-targetSdkVersion" value="34" />
        <preference name="AndroidXEnabled" value="true" />
        <preference name="Scheme" value="http" />
        <edit-config file="app/src/main/AndroidManifest.xml" mode="merge" target="/manifest/application" xmlns:android="http://schemas.android.com/apk/res/android">
            <application android:networkSecurityConfig="@xml/network_security_config" />
        </edit-config>
        <plugin name="cordova-plugin-geolocation" spec="^4.0.2" />
        <plugin name="cordova-plugin-request-location-accuracy" spec="^2.3.0">
            <variable name="PLAY_SERVICES_LOCATION_VERSION" value="16.+" />
        </plugin>
        <hook src="build-helper.js" type="before_build" />
        <resource-file src="resources/android/xml/network_security_config.xml" target="app/src/main/res/xml/network_security_config.xml" />
        <allow-intent href="market:*" />
        <icon background="resources/android/icon/BG/BG --36.png" density="ldpi" foreground="resources/android/icon/FG/FG -36.png" monochrome="resources/android/icon/Mono chrome/FG/FG- MC-36.png" />
        <icon background="resources/android/icon/BG/BG --48.png" density="mdpi" foreground="resources/android/icon/FG/FG -48.png" monochrome="resources/android/icon/Mono chrome/FG/FG- MC-48.png" />
        <icon background="resources/android/icon/BG/BG --72.png" density="hdpi" foreground="resources/android/icon/FG/FG -72.png" monochrome="resources/android/icon/Mono chrome/FG/FG- MC-72.png" />
        <icon background="resources/android/icon/BG/BG --144.png" density="xhdpi" foreground="resources/android/icon/FG/FG -144.png" monochrome="resources/android/icon/Mono chrome/FG/FG- MC-144.png" />
        <icon background="resources/android/icon/BG/BG --192.png" density="xxhdpi" foreground="resources/android/icon/FG/FG -192.png" monochrome="resources/android/icon/Mono chrome/FG/FG- MC-192.png" />
        <icon background="resources/android/icon/BG/BG --512.png" density="xxxhdpi" foreground="resources/android/icon/FG/FG -512.png" monochrome="resources/android/icon/Mono chrome/FG/FG- MC-512.png" />
        <preference name="AndroidWindowSplashScreenAnimatedIcon" value="resources/android/splash/Unvired-logo.png" />
    </platform>
    <platform name="ios">
        <preference name="deployment-target" value="13.0" />
        <hook src="update-deployment-target.js" type="after_platform_add" />
        <config-file mode="add" parent="ITSAppUsesNonExemptEncryption" target="*-Info.plist">
            <false />
        </config-file>
        <allow-intent href="itms:*" />
        <allow-intent href="itms-apps:*" />
        <icon height="57" src="resources/ios/icon/icon.png" width="57" />
        <icon height="114" src="resources/ios/icon/<EMAIL>" width="114" />
        <icon height="29" src="resources/ios/icon/icon-small.png" width="29" />
        <icon height="58" src="resources/ios/icon/<EMAIL>" width="58" />
        <icon height="87" src="resources/ios/icon/<EMAIL>" width="87" />
        <icon height="20" src="resources/ios/icon/icon-20.png" width="20" />
        <icon height="40" src="resources/ios/icon/<EMAIL>" width="40" />
        <icon height="60" src="resources/ios/icon/<EMAIL>" width="60" />
        <icon height="48" src="resources/ios/icon/<EMAIL>" width="48" />
        <icon height="55" src="resources/ios/icon/<EMAIL>" width="55" />
        <icon height="29" src="resources/ios/icon/icon-29.png" width="29" />
        <icon height="58" src="resources/ios/icon/<EMAIL>" width="58" />
        <icon height="87" src="resources/ios/icon/<EMAIL>" width="87" />
        <icon height="40" src="resources/ios/icon/icon-40.png" width="40" />
        <icon height="80" src="resources/ios/icon/<EMAIL>" width="80" />
        <icon height="120" src="resources/ios/icon/<EMAIL>" width="120" />
        <icon height="88" src="resources/ios/icon/<EMAIL>" width="88" />
        <icon height="50" src="resources/ios/icon/icon-50.png" width="50" />
        <icon height="100" src="resources/ios/icon/<EMAIL>" width="100" />
        <icon height="60" src="resources/ios/icon/icon-60.png" width="60" />
        <icon height="120" src="resources/ios/icon/<EMAIL>" width="120" />
        <icon height="180" src="resources/ios/icon/<EMAIL>" width="180" />
        <icon height="72" src="resources/ios/icon/icon-72.png" width="72" />
        <icon height="144" src="resources/ios/icon/<EMAIL>" width="144" />
        <icon height="76" src="resources/ios/icon/icon-76.png" width="76" />
        <icon height="152" src="resources/ios/icon/<EMAIL>" width="152" />
        <icon height="167" src="resources/ios/icon/<EMAIL>" width="167" />
        <icon height="172" src="resources/ios/icon/<EMAIL>" width="172" />
        <icon height="196" src="resources/ios/icon/<EMAIL>" width="196" />
        <icon height="1024" src="resources/ios/icon/icon-1024.png" width="1024" />
        <splash height="480" src="resources/ios/splash/Default~iphone.png" width="320" />
        <splash height="960" src="resources/ios/splash/Default@2x~iphone.png" width="640" />
        <splash height="1024" src="resources/ios/splash/Default-Portrait~ipad.png" width="768" />
        <splash height="768" src="resources/ios/splash/Default-Landscape~ipad.png" width="1024" />
        <splash height="1125" src="resources/ios/splash/Default-Landscape-2436h.png" width="2436" />
        <splash height="1242" src="resources/ios/splash/Default-Landscape-736h.png" width="2208" />
        <splash height="2048" src="resources/ios/splash/Default-Portrait@2x~ipad.png" width="1536" />
        <splash height="1536" src="resources/ios/splash/Default-Landscape@2x~ipad.png" width="2048" />
        <splash height="2732" src="resources/ios/splash/Default-Portrait@~ipadpro.png" width="2048" />
        <splash height="2048" src="resources/ios/splash/Default-Landscape@~ipadpro.png" width="2732" />
        <splash height="1136" src="resources/ios/splash/Default-568h@2x~iphone.png" width="640" />
        <splash height="1334" src="resources/ios/splash/Default-667h.png" width="750" />
        <splash height="2208" src="resources/ios/splash/Default-736h.png" width="1242" />
        <splash height="2436" src="resources/ios/splash/Default-2436h.png" width="1125" />
        <splash height="2732" src="resources/ios/splash/Default@2x~universal~anyany.png" width="2732" />
        <icon height="216" src="resources/ios/icon/<EMAIL>" width="216" />
        <splash height="2688" src="resources/ios/splash/Default-2688h~iphone.png" width="1242" />
        <splash height="1242" src="resources/ios/splash/Default-Landscape-2688h~iphone.png" width="2688" />
        <splash height="1792" src="resources/ios/splash/Default-1792h~iphone.png" width="828" />
        <splash height="828" src="resources/ios/splash/Default-Landscape-1792h~iphone.png" width="1792" />
        <icon height="57" src="resources/ios/icon/icon.png" width="57" />
        <icon height="114" src="resources/ios/icon/<EMAIL>" width="114" />
        <icon height="20" src="resources/ios/icon/icon-20.png" width="20" />
        <icon height="40" src="resources/ios/icon/<EMAIL>" width="40" />
        <icon height="60" src="resources/ios/icon/<EMAIL>" width="60" />
        <icon height="29" src="resources/ios/icon/icon-29.png" width="29" />
        <icon height="58" src="resources/ios/icon/<EMAIL>" width="58" />
        <icon height="87" src="resources/ios/icon/<EMAIL>" width="87" />
        <icon height="48" src="resources/ios/icon/<EMAIL>" width="48" />
        <icon height="55" src="resources/ios/icon/<EMAIL>" width="55" />
        <icon height="88" src="resources/ios/icon/<EMAIL>" width="88" />
        <icon height="172" src="resources/ios/icon/<EMAIL>" width="172" />
        <icon height="196" src="resources/ios/icon/<EMAIL>" width="196" />
        <icon height="216" src="resources/ios/icon/<EMAIL>" width="216" />
        <icon height="40" src="resources/ios/icon/icon-40.png" width="40" />
        <icon height="80" src="resources/ios/icon/<EMAIL>" width="80" />
        <icon height="120" src="resources/ios/icon/<EMAIL>" width="120" />
        <icon height="50" src="resources/ios/icon/icon-50.png" width="50" />
        <icon height="100" src="resources/ios/icon/<EMAIL>" width="100" />
        <icon height="60" src="resources/ios/icon/icon-60.png" width="60" />
        <icon height="120" src="resources/ios/icon/<EMAIL>" width="120" />
        <icon height="180" src="resources/ios/icon/<EMAIL>" width="180" />
        <icon height="72" src="resources/ios/icon/icon-72.png" width="72" />
        <icon height="144" src="resources/ios/icon/<EMAIL>" width="144" />
        <icon height="76" src="resources/ios/icon/icon-76.png" width="76" />
        <icon height="152" src="resources/ios/icon/<EMAIL>" width="152" />
        <icon height="167" src="resources/ios/icon/<EMAIL>" width="167" />
        <icon height="1024" src="resources/ios/icon/icon-1024.png" width="1024" />
        <splash height="1136" src="resources/ios/splash/Default-568h@2x~iphone.png" width="640" />
        <splash height="1334" src="resources/ios/splash/Default-667h.png" width="750" />
        <splash height="2688" src="resources/ios/splash/Default-2688h~iphone.png" width="1242" />
        <splash height="1242" src="resources/ios/splash/Default-Landscape-2688h~iphone.png" width="2688" />
        <splash height="1792" src="resources/ios/splash/Default-1792h~iphone.png" width="828" />
        <splash height="828" src="resources/ios/splash/Default-Landscape-1792h~iphone.png" width="1792" />
        <splash height="2436" src="resources/ios/splash/Default-2436h.png" width="1125" />
        <splash height="1125" src="resources/ios/splash/Default-Landscape-2436h.png" width="2436" />
        <splash height="2208" src="resources/ios/splash/Default-736h.png" width="1242" />
        <splash height="1242" src="resources/ios/splash/Default-Landscape-736h.png" width="2208" />
        <splash height="1536" src="resources/ios/splash/Default-Landscape@2x~ipad.png" width="2048" />
        <splash height="2048" src="resources/ios/splash/Default-Landscape@~ipadpro.png" width="2732" />
        <splash height="768" src="resources/ios/splash/Default-Landscape~ipad.png" width="1024" />
        <splash height="2048" src="resources/ios/splash/Default-Portrait@2x~ipad.png" width="1536" />
        <splash height="2732" src="resources/ios/splash/Default-Portrait@~ipadpro.png" width="2048" />
        <splash height="1024" src="resources/ios/splash/Default-Portrait~ipad.png" width="768" />
        <splash height="960" src="resources/ios/splash/Default@2x~iphone.png" width="640" />
        <splash height="480" src="resources/ios/splash/Default~iphone.png" width="320" />
        <splash height="2732" src="resources/ios/splash/Default@2x~universal~anyany.png" width="2732" />
    </platform>
    <plugin name="cordova-plugin-statusbar" spec="2.4.2" />
    <plugin name="cordova-plugin-device" spec="2.0.2" />
    <plugin name="cordova-plugin-splashscreen" spec="5.0.2" />
    <plugin name="cordova-plugin-ionic-webview" spec="^5.0.0" />
    <plugin name="cordova-plugin-ionic-keyboard" spec="^2.0.5" />
    <allow-navigation href="http://localhost:8100" sessionid="3416e86e" />
    <allow-navigation href="http://**************:8100" sessionid="e27292c5" />
    <allow-navigation href="http://localhost:8101" sessionid="4e43d20c" />
</widget>
