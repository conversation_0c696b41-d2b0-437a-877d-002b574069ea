import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {
  AuthenticateAndActivateResultType,
  LoginParameters,
  LoginType,
  ResultType,
  SettingsResult,
  UnviredCordovaSDK,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { LoadingController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { USER_HEADER } from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import { AppConstants } from 'src/app/shared/app-constants';
import { UserContextService } from 'src/app/services/user-context.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage implements OnInit {
  public appVersion: string = '';
  public versionHide: boolean = true;
  public constants: AppConstants;
  public userFacility: string = '';

  constructor(
    private route: Router,
    public loadingController: LoadingController,
    public translate: TranslateService,
    public dataService: DataService,
    private unviredSDK: UnviredCordovaSDK,
    private userContextService: UserContextService
  ) {
    this.appVersion = this.dataService.getAppVersion();
  }

  ngOnInit() {
    this.constants = new AppConstants();
    window.addEventListener('keyboardDidHide', () => {
      this.versionHide = true;
    });
    window.addEventListener('keyboardWillShow', () => {
      this.versionHide = false;
    });
  }

  getRedirectURL(url) {
    let redirectURL = url.substring(0, url.indexOf('/UMP'));
    return redirectURL;
  }

  async nustarLogin() {
    let loginParameters = new LoginParameters();
    loginParameters.url = this.dataService.getUmpUrl();
    loginParameters.company = this.constants.LOGIN_PARAMS.COMPANY;

    loginParameters.jwtOptions = { app: 'PERMIT' };
    loginParameters.cacheWebData = true;

    // For SAML Login
    loginParameters.loginType = LoginType.saml2;
    loginParameters['redirectURL'] = this.getRedirectURL(loginParameters.url);

    let authenticateResult = await this.unviredSDK.authenticateAndActivate(
      loginParameters
    );
    if (
      authenticateResult.type ==
      AuthenticateAndActivateResultType.auth_activation_success
    ) {
      let settingsResult: SettingsResult = await this.unviredSDK.userSettings();

      await this.presentLoading();

      if (settingsResult) {
        let userId: any = settingsResult.data.USER_ID;

        if (userId) { 
          let customData = {
            USER: [
              {
                USER_HEADER: { USER_ID: userId },
              },
            ],
          };
          let res: any = await this.dataService.getUserById(customData);
          if (res.type == 0 && Object.keys(res.data).length > 0) {
            // Initialize data before navigating to permits page
            await this.initializeData();
            this.displayLandingPage();
          } else {
            // Calling create user
            let userObj = {} as USER_HEADER;
            userObj.P_MODE = 'A';
            userObj.ROLE_NAME = 'ADMIN';
            userObj.FIRST_NAME = settingsResult.data.FULL_NAME.split(' ')[0];
            userObj.LAST_NAME = settingsResult.data.FULL_NAME.split(' ')[1];
            userObj.EMAIL = settingsResult.data.EMAIL;
            userObj.USER_ID = settingsResult.data.USER_ID;
            userObj.PHONE = '';

            let customData = {
              AGENT: 'NUSTAR',
              USERS: [userObj],
            };
            let result: any = await this.dataService.createAgentUser(
              customData
            );
            if (result && result.type == 0) {
              // Initialize data before navigating to permits page
              await this.initializeData();
              this.displayLandingPage();
            }
          }
        }
      }

      this.loadingController?.dismiss();
    }
    console.log('Auth Result: ' + JSON.stringify(authenticateResult, null, 2));
  }

  // Initialize all data needed from home.page.ts
  async initializeData() {
    // Display a loading message while initializing data
    const loading = await this.loadingController.create({
      message: this.translate.instant('Initializing application data...'),
      backdropDismiss: false
    });
    await loading.present();
    
    try {
      // Get user context and customization
      await this.loadcustomizationAndUserContext();
      
      // Get facility data
      await this.dataService.getFacility();
      
      // Get agent data
      await this.dataService.getAgents();
      await this.getUsersServerData();
      
      // Get user data
      await this.dataService.getAllUserData();
      
      // Get skill data
      await this.dataService.getAllSkillData();
      
      // Get structure data for the user facility
      let userContextResult = await this.dataService.getData('USER_CONTEXT_HEADER');
      if (userContextResult && userContextResult.length > 0) {
        this.userFacility = userContextResult[0]?.CURRENT_FACILITY || '';
        
        let customData = {
          STRUCTURE: [
            {
              STRUCTURE_HEADER: {
                FACILITY_ID: this.userFacility,
                DIVISION_ID: '',
                TAG: '',
                NAME: '',
                CATEGORY: '',
                STRUCT_TYPE: '',
                STATUS: '',
                P_MODE: '',
              },
            },
          ],
        };
        
        // Get structures for the user facility
        await this.dataService.getStructures(customData);
        
        // Download permits
        await this.dataService.downloadPermits(null, null, true);
      }
    } catch (error) {
      console.error('Error initializing application data:', error);
    } finally {
      // Close the loading indicator
      await loading.dismiss();
    }
  }

  async loadcustomizationAndUserContext() {
    try {
      // Get User Context
      let result: any = await this.dataService.getUserContext();
      if (result && result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
        if (result?.data?.InfoMessage === undefined) {
          this.unviredSDK.logInfo(
            'LoginPage',
            'loadcustomizationAndUserContext',
            'User Context has been downloaded successfully.'
          );

          if (result?.data?.USER_CONTEXT?.length > 0) {
            localStorage.setItem(
              'userContext',
              JSON.stringify(result.data.USER_CONTEXT[0])
            );

            // Notify app component that user context has been updated
            this.userContextService.notifyUserContextUpdated();
          }
        }
      }

      // Get customization data
      await this.dataService.getCustomization();
    } catch (error) {
      console.error('Error loading customization and user context:', error);
      throw error;
    }
  }

  async getUsersServerData() {
    try {
      let userAgent = await this.dataService.getData('AGENT_HEADER');
      for (const element of userAgent) {
        let customData = {
          AGENT_USER: [
            {
              AGENT_USER_HEADER: {
                AGENT_ID: element?.AGENT_ID,
              },
            },
          ],
        };
        await this.dataService.getAgentUser(customData);
      }
    } catch (error) {
      console.error('Error getting users server data:', error);
      throw error;
    }
  }

  // Navigate to landing page
  async displayLandingPage() {
    // Flag for do customization only when user navigates from login page.
    this.dataService.isStartCustomization = true;
   
    // Navigate directly to permits page instead of home
    this.route.navigate(['permits']);
  }

  contractorLogin() {
    this.route.navigate(['/contractor-login']);
  }

  async presentLoading() {
    const loading = await this.loadingController.create({
      message: 'Please wait...',
    });
    await loading.present();
  }
}
