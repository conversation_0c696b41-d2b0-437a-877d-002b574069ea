import { Component, OnInit } from '@angular/core';
import { Platform } from '@ionic/angular';
import { Router, NavigationExtras } from '@angular/router';
import { UnviredCordovaSDK, LoginParameters, LoginType, LoginListenerType, LoginResult } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { StatusBar } from "@awesome-cordova-plugins/status-bar/ngx";
import { AndroidPermissions } from '@awesome-cordova-plugins/android-permissions/ngx';
import { SplashScreen } from '@awesome-cordova-plugins/splash-screen/ngx';
import { ScreenOrientation } from '@awesome-cordova-plugins/screen-orientation/ngx';
import { TranslateService } from '@ngx-translate/core';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { AppConstants } from 'src/app/shared/app-constants';
import { AppSpecificUtilityService } from 'src/app/services/app-specific-utility.service';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import { ShareDataService } from 'src/app/services/ShareData.service';
declare var cordova: any;
@Component({
  selector: 'app-permissions',
  templateUrl: './permissions.page.html',
  styleUrls: ['./permissions.page.scss'],
})
export class PermissionsPage implements OnInit {

  public constants: AppConstants;
  public permissions: string[];
  public requiredPermissions: string[];
  public devicePlatform = '';
  
  constructor(
    private platform: Platform,
    private splashScreen: SplashScreen,
    private router: Router,
    private unviredSDK: UnviredCordovaSDK,
    private shareData: ShareDataService,
    private screenOrientation: ScreenOrientation,
    private androidPermissions: AndroidPermissions,
    private appSpecificUtility: AppSpecificUtilityService,
    private loader: BusyIndicatorService,
    private translate: TranslateService,
    private statusBar: StatusBar,
    private device: Device
  ) {
    this.constants = new AppConstants();
    this.requiredPermissions = [
      this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE,
      this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE,
      this.androidPermissions.PERMISSION.READ_PHONE_STATE,
      this.androidPermissions.PERMISSION.CAMERA,
      this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION,
      this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION,
      "android.permission.READ_MEDIA_IMAGES",
      "android.permission.POST_NOTIFICATIONS"
    ];
    console.log("required permissions are",this.requiredPermissions)
    this.devicePlatform = this.shareData.getDevicePlatform();
    if(parseInt(this.device.version) > 12){
      this.requiredPermissions =  this.requiredPermissions.filter( e => e !== this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE && e!== this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE )
      this.requiredPermissions.push( "android.permission.READ_MEDIA_IMAGES",  "android.permission.POST_NOTIFICATIONS" )
    }
  }

  ngOnInit() { }

  async ionViewWillEnter() {
    this.splashScreen.hide();
    await this.showPermissionsAlert();
    var androidpermissionsList = cordova.plugins.permissions;
    // console.log("permissionss" , androidpermissionsList)
  }

  async ionViewDidLoad() {
    this.statusBar.show();
    this.statusBar.overlaysWebView(true);
    this.statusBar.styleLightContent();
  }

  async showPermissionsAlert() {
    this.permissions = [];
    if(parseInt(this.device.version) > 12){
      // let storageReadMediaImages = await this.androidPermissions.checkPermission("android.permission.READ_MEDIA_IMAGES");
      // if (storageReadMediaImages.hasPermission == false ) {
      //   this.permissions.push("Storage");
      // }

      // let pushNotif = await this.androidPermissions.checkPermission("android.permission.POST_NOTIFICATIONS");
      // if(pushNotif.hasPermission == false){
      //   this.permissions.push("Notification")
      // }

    } else{
      let storageRead= await this.androidPermissions.checkPermission( this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE);
      let storageWrite = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE);
      if (storageRead.hasPermission == false ||  storageWrite.hasPermission == false ) {
        this.permissions.push("Storage");
      }
    }
    

    let phoneState = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE);
    if (phoneState.hasPermission == false) {
      this.permissions.push("Phone State");
    }

    let cameraPermission = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.CAMERA);
    if (cameraPermission.hasPermission == false) {
      this.permissions.push("Camera");
    }

    let fineLocation = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION);
    let coarseLocation = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION);
    if (coarseLocation.hasPermission == false || fineLocation.hasPermission == false) {
      this.permissions.push("Location");
    }

    if (this.permissions.length > 0) {
      // If any permissions is denied or cancelled, permissions array length would be greater than zero
      // When location is not allowed by user
      if (this.permissions[0] === 'Location') {
        this.permissions = [];
        // Request for location plugin again
        let locationPermissions = [
          this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION,
          this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION
        ];
        await this.androidPermissions.requestPermissions(locationPermissions);
        await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION);
        await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION);
        // Start login when all permissions are allowed except location
        await this.initializeLoginProcess();
      }
      // Except from location if any other permission is denied then DON'T DO ANYTHING [ Stay on same screen ]
    } else {
      // Start login when all permissions are allowed
      await this.initializeLoginProcess();
    }
  }

  async checkHasPermissions() {
    let requestedPermissions = await this.androidPermissions.requestPermissions(this.requiredPermissions);
    if (requestedPermissions.hasPermission == false) {
      await this.showPermissionsAlert();
    } else {
      await this.initializeLoginProcess();
    }
  }

  exitApp() {
    console.log("exit the app ")
    // navigator['app'].exitApp();
  }

  // Initalize login process for different mode of login and activation
  async initializeLoginProcess() {
    try {
      let loginParameters = new LoginParameters();
      loginParameters.appName = this.constants.LOGIN_PARAMS.APP_NAME;
      loginParameters.metadataPath = this.constants.LOGIN_PARAMS.METADATA_PATH;
      loginParameters.autoSyncTime = '10'

      let loginResult: LoginResult = null;
      this.unviredSDK.logDebug('PermissionsPage', 'initializeLoginProcess', 'Calling login()');
      loginResult = await this.unviredSDK.login(loginParameters);
      const dataToSend: NavigationExtras = { queryParams: { type: loginResult.type } };
      switch (loginResult.type) {
        case LoginListenerType.auth_activation_required:
          // Display Login Screen |auth_activation_required|
          this.displayLoginPage(dataToSend);
          break;
        case LoginListenerType.app_requires_login:
          // Display Login Screen |app_requires_login|
          this.displayLoginPage(dataToSend);
          break;
        case LoginListenerType.login_success:
          this.displayLandingPage();
          break;
      }
    } catch (error) {
      this.unviredSDK.logError('PermissionsPage', 'initializeLoginProcces()', 'ERROR: ' + error);
      this.loader.showToast(this.translate.instant("Login failed! Please try again"));
    }
  }
  
  displayLoginPage(data:any) {
    if (this.devicePlatform === 'browser') {
      if (this.platform.is('iphone') || this.platform.is('android')) {
        this.appSpecificUtility.showMobileView.next(true);
      }
      this.router.navigate(['login'], data);
    } else {
      if (this.platform.is('tablet') || this.platform.is('ipad')) {
        if (this.platform.isLandscape()) {
          this.appSpecificUtility.showMobileView.next(false);
          this.appSpecificUtility.deviceMode.next(this.constants.DEVICE_TYPE.TABLET);
        } else {
          this.appSpecificUtility.showMobileView.next(true);
          this.appSpecificUtility.deviceMode.next(this.constants.DEVICE_TYPE.PHONE);
        }
      }
      this.router.navigate(['mobile-login'], data);
    }
  }
  // Navigate to landing page
  displayLandingPage() {

    this.unviredSDK.logDebug('PermissionsPage', 'displayLandingPage()', 'Ionic Platforms Array: ' + this.platform.platforms());
    this.unviredSDK.logDebug('PermissionsPage', 'displayLandingPage()', 'Orientation: ' + this.platform.isLandscape() ? "Landscape" : "Portrait");

    try {
      var that = this;
      // Only for windows platform |Native|
      if (this.devicePlatform === 'windows') {
        // Navigate to home page
        that.router.navigate(['mobile-home']);
        // Pass the veiw flag with device and orientation specific
        that.appSpecificUtility.showMobileView.next(false);
      } else if (this.devicePlatform !== 'browser') { // Not for browser |Native Application|
        // Only for phones |iphone / android phone|
        if (!this.platform.is('tablet')) {
          // Pass the device mode flag
          this.appSpecificUtility.deviceMode.next(this.constants.DEVICE_TYPE.PHONE);
          if ((this.platform.is('iphone')) || (this.platform.is('android'))) {
            // Navigate to tabs page
            this.router.navigate(['mobile-home']);
            // Pass the veiw flag with device and orientation specific
            this.appSpecificUtility.showMobileView.next(true);
          }
          // Only for tablets |ipad / android tab|
        } else if (this.platform.is('tablet')) {
          // Pass the device mode flag
          if ((this.platform.is('ipad')) || (this.platform.is('android'))) {
            // InitialRotation
            const initialorientation = this.screenOrientation.type;
            if (initialorientation === 'landscape-primary' || initialorientation === 'landscape-secondary') {
              // Navigate to home page
              this.loadScreenInLandscapeMode();
            } else {
              this.loadScreenInPortraitMode();
            }
          }
        }
      } else { // Browser
        if (this.platform.is('iphone')) {
          this.loadScreenInPortraitMode()
        } else if (this.platform.is('ipad')) {
          if (this.platform.isLandscape()) {
            this.loadScreenInLandscapeMode()
          } else {
            this.loadScreenInPortraitMode()
          }
        } else if (this.platform.is('android')) {
          if (this.platform.is('phablet')) {
            this.loadScreenInPortraitMode()
          } else if (this.platform.is('tablet')) {
            if (this.platform.isLandscape()) {
              this.loadScreenInLandscapeMode()
            } else {
              this.loadScreenInPortraitMode()
            }
          } else {
            this.loadScreenInLandscapeMode()
          }
        } else { // Desktop or any other platform not listed.
          this.loadScreenInLandscapeMode()
        }
      }
    } catch (error) {
      this.unviredSDK.logError('PermissionsPage', 'displayLandingPage()', 'ERROR: ' + error);
    }
  }

  loadScreenInPortraitMode() {
    this.appSpecificUtility.deviceMode.next(this.constants.DEVICE_TYPE.PHONE);
    this.router.navigate(['mobile-home']);
    this.appSpecificUtility.showMobileView.next(true);
  }

  loadScreenInLandscapeMode() {
    this.appSpecificUtility.deviceMode.next(this.constants.DEVICE_TYPE.TABLET);
    this.router.navigate(['mobile-home']);
    this.appSpecificUtility.showMobileView.next(false);
  }
}

