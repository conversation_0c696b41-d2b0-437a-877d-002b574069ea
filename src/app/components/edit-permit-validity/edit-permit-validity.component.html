<ion-header mode="ios">
  <ion-toolbar color="primary" mode="ios">
    <ion-title>Edit Validity</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="cancel()"> <ion-icon slot="icon-only" name="close"></ion-icon></ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-progress-bar type="indeterminate" *ngIf="progressbar"></ion-progress-bar>
</ion-header>
<ion-content style="--padding-top: 0px; --padding-bottom: 16px; --padding-start: 16px; --padding-end: 16px;">
  <div style="padding-top: 16px;">
    <form [formGroup]="createForm">

      <!-- Start Date -->
      <div style="margin-bottom: 20px;">
        <div style="position: relative;">
          <label style="position: absolute; top: -8px; left: 16px; background: white; padding: 0 4px; font-size: 12px; color: #666; z-index: 1;">
            Start Date <span style="color: #eb445a;">*</span>
          </label>
          <input
            style="width: 100%; padding: 12px 16px 12px 45px; border: 1px solid var(--ion-color-step-300, #b3b3b3); border-radius: 4px; height: 43px; background: transparent; font-size: 14px; box-sizing: border-box; font-family: inherit;"
            placeholder="Select Start Date"
            readonly
            [owlDateTime]="startDatePicker"
            [owlDateTimeTrigger]="startDatePicker"
            formControlName="startDate">
          <ion-icon name="calendar-outline" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: #00629b; pointer-events: none;"></ion-icon>
        </div>
        <owl-date-time #startDatePicker [pickerType]="'both'" [hour12Timer]="true"
          (afterPickerClosed)="onStartDateChange($event)"></owl-date-time>
      </div>

      <!-- End Date -->
      <div style="margin-bottom: 20px;">
        <div style="position: relative;">
          <label style="position: absolute; top: -8px; left: 16px; background: white; padding: 0 4px; font-size: 12px; color: #666; z-index: 1;">
            End Date <span style="color: #eb445a;">*</span>
          </label>
          <input
            style="width: 100%; padding: 12px 16px 12px 45px; border: 1px solid var(--ion-color-step-300, #b3b3b3); border-radius: 4px; height: 43px; background: transparent; font-size: 14px; box-sizing: border-box; font-family: inherit;"
            placeholder="Select End Date"
            readonly
            [owlDateTime]="endDatePicker"
            [owlDateTimeTrigger]="endDatePicker"
            formControlName="endDate">
          <ion-icon name="calendar-outline" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: #00629b; pointer-events: none;"></ion-icon>
        </div>
        <owl-date-time #endDatePicker [pickerType]="'both'" [hour12Timer]="true"
          [min]="minEndDate" [max]="maxEndDate"
          (afterPickerClosed)="onEndDateChange($event)"></owl-date-time>
        <ion-note style="font-size: 12px; color: #666; margin-top: 4px; display: block;">
          Maximum 8 hours from start date
        </ion-note>
        <!-- Validation error message -->
        <ion-text color="danger" style="font-size: 12px; margin-top: 4px; display: block;"
          *ngIf="createForm.get('endDate')?.invalid && createForm.get('endDate')?.touched">
          <span *ngIf="createForm.get('endDate')?.errors?.['maxDuration']">
            End date cannot be more than 8 hours from start date
          </span>
          <span *ngIf="createForm.get('endDate')?.errors?.['minDate']">
            End date cannot be before start date
          </span>
        </ion-text>
      </div>

    </form>

    <div style="margin-top: 16px;text-align: center;" *ngIf="errorMessage.length > 0">
      <ion-text style="color: indianred;" class="textCenter">
        <span>{{errorMessage}}</span>
      </ion-text>
    </div>
  </div>
</ion-content>

<ion-footer mode="ios">
  <ion-toolbar mode="ios">
    <ion-button slot="end" color="danger" mode="md" (click)="cancel()">Cancel</ion-button>
    <ion-button slot="end" color="success" mode="md" [disabled]="!createForm.valid"
      (click)="save()">Save</ion-button>
  </ion-toolbar>
</ion-footer>