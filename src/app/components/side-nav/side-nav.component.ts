import { Component, OnInit, Renderer2 } from '@angular/core';
import { Router } from '@angular/router';
import { DataService } from 'src/app/services/data.service';

@Component({
  selector: 'app-side-nav',
  templateUrl: './side-nav.component.html',
  styleUrls: ['./side-nav.component.scss'],
  host: {
    '[class.collapsed]': 'isCollapsed'
  }
})
export class SideNavComponent implements OnInit {
  // Set isCollapsed to true for collapsed mode by default
  public isCollapsed = true;
  public selectedOption = 'permits';
  public userData: any[] = [];
  // Keep isExpanded in sync (false when collapsed)
  isExpanded: boolean = false;

  // User permissions for menu visibility
  public userPermissions = {
    agents: false,
    configuration: false,
    facilities: false,
    reports: false,
    users: false
  };
  
  constructor(
    private router: Router,
    private dataService: DataService,
    private renderer: Renderer2
  ) {}

  async ngOnInit() {
    try {
      this.selectedOption = 'permits'; // Ensure permits is selected
      await this.getUserData();
      
      // Update content container initially based on collapsed state
      setTimeout(() => {
        const contentContainer = document.querySelector('.content-container');
        if (contentContainer && this.isCollapsed) {
          this.renderer.setStyle(contentContainer, 'margin-left', '60px');
          this.renderer.setStyle(contentContainer, 'width', 'calc(100% - 60px)');
        }
      }, 0);
      
      // Navigate to permits page on component init
      if (this.router.url === '/' || this.router.url === '/home') {
        this.router.navigate(['/permits']);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  }

  async getUserData() {
    try {
      this.userData = await this.dataService.getData('USER_ROLE') || [];
      console.log('User role data:', this.userData);

      // Set user permissions based on USER_ROLE data
      this.setUserPermissions();
    } catch (error) {
      console.error('Error getting user data:', error);
      this.userData = [];
      // Reset permissions if no data
      this.resetPermissions();
    }
  }

  /**
   * Set user permissions based on USER_ROLE data
   */
  private setUserPermissions() {
    // Reset permissions first
    this.resetPermissions();

    if (this.userData && this.userData.length > 0) {
      // Check each user role record for permissions
      this.userData.forEach(userRole => {
        // Agents menu - AGENT_MGMT = 'true'
        if (userRole.AGENT_MGMT === 'true') {
          this.userPermissions.agents = true;
        }

        // Configuration menu - CONFIGURATION = 'true'
        if (userRole.CONFIGURATION === 'true') {
          this.userPermissions.configuration = true;
        }

        // Facilities menu - FACILITY_MGMT = 'true'
        if (userRole.FACILITY_MGMT === 'true') {
          this.userPermissions.facilities = true;
        }

        // Reports menu - REPORT = 'true'
        if (userRole.REPORT === 'true') {
          this.userPermissions.reports = true;
        }

        // Users menu - USER_MGMT = 'true'
        if (userRole.USER_MGMT === 'true') {
          this.userPermissions.users = true;
        }
      });
    }

    console.log('User permissions set:', this.userPermissions);
  }

  /**
   * Reset all permissions to false
   */
  private resetPermissions() {
    this.userPermissions = {
      agents: false,
      configuration: false,
      facilities: false,
      reports: false,
      users: false
    };
  }

  toggleCollapse() {
    this.isCollapsed = !this.isCollapsed;
    
    // Update content container based on navigation state
    const contentContainer = document.querySelector('.content-container');
    if (contentContainer) {
      if (this.isCollapsed) {
        this.renderer.setStyle(contentContainer, 'margin-left', '60px');
        this.renderer.setStyle(contentContainer, 'width', 'calc(100% - 60px)');
      } else {
        this.renderer.setStyle(contentContainer, 'margin-left', '240px');
        this.renderer.setStyle(contentContainer, 'width', 'calc(100% - 240px)');
      }
    }
  }

  navigate(route: string) {
    this.selectedOption = route;
    this.router.navigate([`/${route}`]);
  }

  /**
   * Public method to refresh user permissions
   * Can be called from parent components when user data is updated
   */
  public async refreshPermissions() {
    console.log('Refreshing user permissions...');
    await this.getUserData();
  }
}
